#!/usr/bin/env python3
"""
ERP System Command Line Interface
Main entry point for server management, addon operations, and testing
"""
import sys
import os
import argparse
import asyncio
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from erp.config import config
from erp.server import ERPServer
from erp.async_server import create_app
from erp.addons.loader import AddonLoader
from erp.addons.manager import AddonManager, AddonState
from erp.addons.async_loader import AsyncAddonLoader
from erp.database.registry import DatabaseRegistry
from erp.database.async_registry import AsyncDatabaseRegistry


class ERPCommandLine:
    """ERP Command Line Interface"""
    
    def __init__(self):
        self.parser = self._create_parser()
    
    def _create_parser(self):
        """Create argument parser"""
        parser = argparse.ArgumentParser(
            description='ERP System Command Line Interface',
            prog='erp-bin'
        )
        
        # Global options
        parser.add_argument('--config', '-c', help='Configuration file path')
        parser.add_argument('--db-name', help='Database name')
        parser.add_argument('--addons-path', help='Addons path')
        parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
        
        # Subcommands
        subparsers = parser.add_subparsers(dest='command', help='Available commands')
        
        # Server commands
        self._add_server_commands(subparsers)
        
        # Addon commands
        self._add_addon_commands(subparsers)
        
        # Database commands
        self._add_database_commands(subparsers)
        
        # Test commands
        self._add_test_commands(subparsers)
        
        return parser
    
    def _add_server_commands(self, subparsers):
        """Add server-related commands"""
        # Start server
        start_parser = subparsers.add_parser('start', help='Start ERP server')
        start_parser.add_argument('--host', default='127.0.0.1', help='Server host')
        start_parser.add_argument('--port', type=int, default=8069, help='Server port')
        start_parser.add_argument('--debug', action='store_true', help='Debug mode')
        start_parser.add_argument('--reload', action='store_true', help='Auto-reload (async only)')
        start_parser.add_argument('--workers', type=int, default=1, help='Worker processes')
        start_parser.add_argument('--async-server', action='store_true', help='Use async server')
        
        # Stop server
        subparsers.add_parser('stop', help='Stop ERP server')
        
        # Server status
        subparsers.add_parser('status', help='Show server status')
    
    def _add_addon_commands(self, subparsers):
        """Add addon-related commands"""
        # List addons
        list_parser = subparsers.add_parser('list', help='List addons')
        list_parser.add_argument('--installed', action='store_true', help='Show only installed addons')
        list_parser.add_argument('--available', action='store_true', help='Show only available addons')
        
        # Install addon
        install_parser = subparsers.add_parser('install', help='Install addon')
        install_parser.add_argument('addon_name', help='Addon name to install')
        install_parser.add_argument('--force', action='store_true', help='Force installation')
        
        # Uninstall addon
        uninstall_parser = subparsers.add_parser('uninstall', help='Uninstall addon')
        uninstall_parser.add_argument('addon_name', help='Addon name to uninstall')
        uninstall_parser.add_argument('--force', action='store_true', help='Force uninstallation')
        
        # Upgrade addon
        upgrade_parser = subparsers.add_parser('upgrade', help='Upgrade addon')
        upgrade_parser.add_argument('addon_name', nargs='?', help='Addon name to upgrade (all if not specified)')
        upgrade_parser.add_argument('--all', action='store_true', help='Upgrade all addons')
        
        # Addon info
        info_parser = subparsers.add_parser('info', help='Show addon information')
        info_parser.add_argument('addon_name', help='Addon name')
    
    def _add_database_commands(self, subparsers):
        """Add database-related commands"""
        # Create database
        create_db_parser = subparsers.add_parser('create-db', help='Create database')
        create_db_parser.add_argument('db_name', help='Database name')
        create_db_parser.add_argument('--demo', action='store_true', help='Install demo data')
        
        # Drop database
        drop_db_parser = subparsers.add_parser('drop-db', help='Drop database')
        drop_db_parser.add_argument('db_name', help='Database name')
        drop_db_parser.add_argument('--force', action='store_true', help='Force drop')
        
        # List databases
        subparsers.add_parser('list-db', help='List databases')
        
        # Database backup
        backup_parser = subparsers.add_parser('backup', help='Backup database')
        backup_parser.add_argument('db_name', help='Database name')
        backup_parser.add_argument('--output', '-o', help='Output file path')
        
        # Database restore
        restore_parser = subparsers.add_parser('restore', help='Restore database')
        restore_parser.add_argument('db_name', help='Database name')
        restore_parser.add_argument('backup_file', help='Backup file path')
    
    def _add_test_commands(self, subparsers):
        """Add test-related commands"""
        # Run tests
        test_parser = subparsers.add_parser('test', help='Run tests')
        test_parser.add_argument('--module', '-m', help='Test specific module')
        test_parser.add_argument('--pattern', '-p', help='Test pattern')
        test_parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
        test_parser.add_argument('--async-tests', action='store_true', help='Run async tests')
        test_parser.add_argument('--performance', action='store_true', help='Run performance tests')
        
        # Test database setup
        subparsers.add_parser('test-setup', help='Setup test database')
        
        # Test database cleanup
        subparsers.add_parser('test-cleanup', help='Cleanup test database')
    
    def run(self, args=None):
        """Run the command line interface"""
        args = self.parser.parse_args(args)
        
        # Apply global configuration
        self._apply_global_config(args)
        
        if not args.command:
            self.parser.print_help()
            return 1
        
        # Route to appropriate handler
        try:
            if args.command == 'start':
                return self._handle_start(args)
            elif args.command == 'stop':
                return self._handle_stop(args)
            elif args.command == 'status':
                return self._handle_status(args)
            elif args.command == 'list':
                return self._handle_list(args)
            elif args.command == 'install':
                return self._handle_install(args)
            elif args.command == 'uninstall':
                return self._handle_uninstall(args)
            elif args.command == 'upgrade':
                return self._handle_upgrade(args)
            elif args.command == 'info':
                return self._handle_info(args)
            elif args.command == 'create-db':
                return self._handle_create_db(args)
            elif args.command == 'drop-db':
                return self._handle_drop_db(args)
            elif args.command == 'list-db':
                return self._handle_list_db(args)
            elif args.command == 'backup':
                return self._handle_backup(args)
            elif args.command == 'restore':
                return self._handle_restore(args)
            elif args.command == 'test':
                return self._handle_test(args)
            elif args.command == 'test-setup':
                return self._handle_test_setup(args)
            elif args.command == 'test-cleanup':
                return self._handle_test_cleanup(args)
            else:
                print(f"Unknown command: {args.command}")
                return 1
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return 1
        except Exception as e:
            if args.verbose:
                import traceback
                traceback.print_exc()
            else:
                print(f"Error: {e}")
            return 1
    
    def _apply_global_config(self, args):
        """Apply global configuration options"""
        if args.config:
            config.config_file = args.config
            config._load_config()
        
        if args.db_name:
            config.set('options', 'db_name', args.db_name)
        
        if args.addons_path:
            config.set('options', 'addons_path', args.addons_path)
    
    def _handle_start(self, args):
        """Handle start command"""
        print("Starting ERP server...")
        print(f"Configuration: {config.config_file}")
        print(f"Database: {config.get('options', 'db_name')}")
        print(f"Addons path: {config.addons_path}")
        
        if args.async_server:
            return self._start_async_server(args)
        else:
            return self._start_sync_server(args)
    
    def _start_sync_server(self, args):
        """Start synchronous server"""
        try:
            server = ERPServer()
            server.run(host=args.host, port=args.port, debug=args.debug)
            return 0
        except Exception as e:
            print(f"Failed to start server: {e}")
            return 1
    
    def _start_async_server(self, args):
        """Start asynchronous server"""
        try:
            import uvicorn
            app = create_app()
            
            uvicorn.run(
                app,
                host=args.host,
                port=args.port,
                reload=args.reload,
                workers=args.workers if not args.reload else 1,
                log_level="info",
                access_log=True
            )
            return 0
        except Exception as e:
            print(f"Failed to start async server: {e}")
            return 1
    
    def _handle_stop(self, args):
        """Handle stop command"""
        print("Stop command not implemented yet")
        return 0
    
    def _handle_status(self, args):
        """Handle status command"""
        print("Status command not implemented yet")
        return 0

    def _handle_list(self, args):
        """Handle list addons command"""
        try:
            manager = AddonManager()
            addons = manager.discover_addons()

            print(f"Found {len(addons)} addons:")
            print("-" * 70)

            for name, addon_info in addons.items():
                # Status icon based on state
                status_icons = {
                    AddonState.INSTALLED: "✓",
                    AddonState.UNINSTALLED: "○",
                    AddonState.TO_INSTALL: "⏳",
                    AddonState.TO_UPGRADE: "⬆",
                    AddonState.TO_REMOVE: "⬇",
                    AddonState.BROKEN: "✗"
                }

                icon = status_icons.get(addon_info.state, "?")
                state_name = addon_info.state.value.replace('_', ' ').title()

                # Show installed vs available version
                version_info = addon_info.available_version
                if addon_info.installed_version and addon_info.installed_version != addon_info.available_version:
                    version_info = f"{addon_info.installed_version} → {addon_info.available_version}"

                print(f"{icon} {name} ({version_info}) - {state_name}")
                if addon_info.manifest.description:
                    print(f"    {addon_info.manifest.description}")
                if addon_info.dependencies:
                    print(f"    Dependencies: {', '.join(addon_info.dependencies)}")
                print()

            return 0
        except Exception as e:
            print(f"Failed to list addons: {e}")
            return 1

    def _handle_install(self, args):
        """Handle install addon command"""
        try:
            manager = AddonManager()
            manager.discover_addons()

            print(f"Installing addon: {args.addon_name}")

            # Check if addon exists
            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                print(f"Addon '{args.addon_name}' not found")
                return 1

            # Check dependencies
            if not args.force:
                deps_ok, dep_errors = manager.check_dependencies(args.addon_name)
                if not deps_ok:
                    print(f"Dependency issues:")
                    for error in dep_errors:
                        print(f"  - {error}")
                    print("Use --force to install anyway")
                    return 1

            # Install addon
            success = manager.install_addon(args.addon_name, force=args.force)
            if success:
                print(f"✓ Addon '{args.addon_name}' installed successfully")
                return 0
            else:
                print(f"✗ Failed to install addon '{args.addon_name}'")
                return 1
        except Exception as e:
            print(f"Failed to install addon: {e}")
            return 1

    def _handle_uninstall(self, args):
        """Handle uninstall addon command"""
        try:
            manager = AddonManager()
            manager.discover_addons()

            print(f"Uninstalling addon: {args.addon_name}")

            # Check if addon exists
            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                print(f"Addon '{args.addon_name}' not found")
                return 1

            # Check dependents
            if not args.force and addon_info.dependents:
                installed_dependents = []
                for dependent in addon_info.dependents:
                    dep_info = manager.get_addon_info(dependent)
                    if dep_info and dep_info.state == AddonState.INSTALLED:
                        installed_dependents.append(dependent)

                if installed_dependents:
                    print(f"Cannot uninstall '{args.addon_name}': required by {', '.join(installed_dependents)}")
                    print("Use --force to uninstall anyway")
                    return 1

            # Uninstall addon
            success = manager.uninstall_addon(args.addon_name, force=args.force)
            if success:
                print(f"✓ Addon '{args.addon_name}' uninstalled successfully")
                return 0
            else:
                print(f"✗ Failed to uninstall addon '{args.addon_name}'")
                return 1
        except Exception as e:
            print(f"Failed to uninstall addon: {e}")
            return 1

    def _handle_upgrade(self, args):
        """Handle upgrade addon command"""
        try:
            manager = AddonManager()
            manager.discover_addons()

            if args.all or not args.addon_name:
                print("Upgrading all addons...")
                # Get all addons that need upgrade
                addons_to_upgrade = manager.list_addons(AddonState.TO_UPGRADE)

                if not addons_to_upgrade:
                    print("No addons need upgrading")
                    return 0

                success_count = 0
                for addon_name in addons_to_upgrade:
                    print(f"Upgrading {addon_name}...")
                    if manager.upgrade_addon(addon_name, force=args.force):
                        success_count += 1
                    else:
                        print(f"Failed to upgrade {addon_name}")

                print(f"Successfully upgraded {success_count}/{len(addons_to_upgrade)} addons")
                return 0 if success_count == len(addons_to_upgrade) else 1
            else:
                print(f"Upgrading addon: {args.addon_name}")

                # Check if addon exists
                addon_info = manager.get_addon_info(args.addon_name)
                if not addon_info:
                    print(f"Addon '{args.addon_name}' not found")
                    return 1

                # Upgrade addon
                success = manager.upgrade_addon(args.addon_name, force=args.force)
                if success:
                    print(f"✓ Addon '{args.addon_name}' upgraded successfully")
                    return 0
                else:
                    print(f"✗ Failed to upgrade addon '{args.addon_name}'")
                    return 1
        except Exception as e:
            print(f"Failed to upgrade addon: {e}")
            return 1

    def _handle_info(self, args):
        """Handle addon info command"""
        try:
            manager = AddonManager()
            manager.discover_addons()

            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                print(f"Addon '{args.addon_name}' not found")
                return 1

            manifest = addon_info.manifest
            print(f"Addon Information: {args.addon_name}")
            print("=" * 70)
            print(f"Name: {manifest.name}")
            print(f"Version: {manifest.version}")
            print(f"Author: {manifest.author}")
            print(f"Category: {manifest.category}")
            print(f"Description: {manifest.description}")
            print(f"State: {addon_info.state.value.replace('_', ' ').title()}")

            if addon_info.installed_version:
                print(f"Installed Version: {addon_info.installed_version}")
            if addon_info.available_version != addon_info.installed_version:
                print(f"Available Version: {addon_info.available_version}")
            if addon_info.install_date:
                print(f"Install Date: {addon_info.install_date.strftime('%Y-%m-%d %H:%M:%S')}")

            print(f"Dependencies: {', '.join(addon_info.dependencies) if addon_info.dependencies else 'None'}")
            print(f"Dependents: {', '.join(addon_info.dependents) if addon_info.dependents else 'None'}")
            print(f"Installable: {'Yes' if manifest.installable else 'No'}")
            print(f"Auto Install: {'Yes' if manifest.auto_install else 'No'}")
            print(f"Path: {manifest.addon_path}")

            # Show dependency tree
            print("\nDependency Tree:")
            tree = manager.get_dependency_tree(args.addon_name)
            self._print_dependency_tree(tree, indent=0)

            # Validate integrity
            is_valid, errors = manager.validate_addon_integrity(args.addon_name)
            print(f"\nIntegrity Check: {'✓ Valid' if is_valid else '✗ Invalid'}")
            if errors:
                for error in errors:
                    print(f"  - {error}")

            return 0
        except Exception as e:
            print(f"Failed to get addon info: {e}")
            return 1

    def _print_dependency_tree(self, tree, indent=0):
        """Print dependency tree recursively"""
        prefix = "  " * indent
        name = tree.get('name', 'unknown')
        state = tree.get('state', 'unknown')
        version = tree.get('version', 'unknown')

        print(f"{prefix}- {name} ({version}) [{state}]")

        if tree.get('not_found'):
            print(f"{prefix}  ⚠ Not found")
        elif tree.get('max_depth_reached'):
            print(f"{prefix}  ... (max depth reached)")
        else:
            for dep in tree.get('dependencies', []):
                self._print_dependency_tree(dep, indent + 1)

    def _handle_create_db(self, args):
        """Handle create database command"""
        print(f"Creating database: {args.db_name}")
        print("Database creation functionality not implemented yet")
        return 0

    def _handle_drop_db(self, args):
        """Handle drop database command"""
        print(f"Dropping database: {args.db_name}")
        print("Database drop functionality not implemented yet")
        return 0

    def _handle_list_db(self, args):
        """Handle list databases command"""
        print("Listing databases...")
        print("Database listing functionality not implemented yet")
        return 0

    def _handle_backup(self, args):
        """Handle database backup command"""
        print(f"Backing up database: {args.db_name}")
        print("Database backup functionality not implemented yet")
        return 0

    def _handle_restore(self, args):
        """Handle database restore command"""
        print(f"Restoring database: {args.db_name}")
        print("Database restore functionality not implemented yet")
        return 0

    def _handle_test(self, args):
        """Handle test command"""
        try:
            if args.async_tests:
                return self._run_async_tests(args)
            else:
                return self._run_sync_tests(args)
        except Exception as e:
            print(f"Failed to run tests: {e}")
            return 1

    def _run_sync_tests(self, args):
        """Run synchronous tests"""
        print("Running tests with pytest framework...")

        try:
            import subprocess

            # Build pytest command
            cmd = ['python', '-m', 'pytest']

            # Add test selection based on args
            if hasattr(args, 'module') and args.module:
                cmd.extend(['-k', args.module])

            if hasattr(args, 'pattern') and args.pattern:
                cmd.extend(['-k', args.pattern])

            if hasattr(args, 'coverage') and args.coverage:
                cmd.extend(['--cov=erp', '--cov-report=term-missing'])

            if hasattr(args, 'performance') and args.performance:
                cmd.extend(['-m', 'performance'])
            else:
                # Skip performance tests by default
                cmd.extend(['-m', 'not performance'])

            # Add verbose output
            cmd.append('-v')

            # Add test directory
            cmd.append('tests/')

            print(f"Running: {' '.join(cmd)}")

            # Run pytest
            result = subprocess.run(cmd, cwd=os.path.dirname(__file__))
            return result.returncode

        except Exception as e:
            print(f"Failed to run pytest: {e}")
            # Fallback to legacy test system
            try:
                from test_system import main as run_legacy_tests
                run_legacy_tests()
                return 0
            except Exception as e2:
                print(f"Legacy tests also failed: {e2}")
                return 1

    def _run_async_tests(self, args):
        """Run asynchronous tests"""
        print("Running asynchronous tests with pytest...")

        try:
            import subprocess

            # Build pytest command for async tests
            cmd = ['python', '-m', 'pytest', '-m', 'async_test', '-v', 'tests/']

            if hasattr(args, 'coverage') and args.coverage:
                cmd.extend(['--cov=erp', '--cov-report=term-missing'])

            print(f"Running: {' '.join(cmd)}")

            # Run pytest
            result = subprocess.run(cmd, cwd=os.path.dirname(__file__))
            return result.returncode

        except Exception as e:
            print(f"Failed to run async pytest: {e}")
            # Fallback to legacy async test system
            try:
                from test_async_system import main as run_async_tests
                result = asyncio.run(run_async_tests())
                return result
            except Exception as e2:
                print(f"Legacy async tests also failed: {e2}")
                return 1

    def _handle_test_setup(self, args):
        """Handle test setup command"""
        print("Setting up test environment...")
        print("Test setup functionality not implemented yet")
        return 0

    def _handle_test_cleanup(self, args):
        """Handle test cleanup command"""
        print("Cleaning up test environment...")
        print("Test cleanup functionality not implemented yet")
        return 0


def main():
    """Main entry point"""
    cli = ERPCommandLine()
    return cli.run()


if __name__ == '__main__':
    sys.exit(main())
