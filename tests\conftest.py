"""
Pytest configuration and fixtures for ERP system tests
"""
import pytest
import asyncio
import tempfile
import shutil
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from erp.config import config
from erp.addons.loader import AddonLoader
from erp.models.base import ModelRegistry

# Optional async imports - may not be available in all environments
try:
    from erp.addons.async_loader import AsyncAddonLoader
    from erp.models.async_base import AsyncModelRegistry
    from erp.database.registry import DatabaseRegistry
    from erp.database.async_registry import AsyncDatabaseRegistry
    ASYNC_AVAILABLE = True
except ImportError:
    ASYNC_AVAILABLE = False
    AsyncAddonLoader = None
    AsyncModelRegistry = None
    DatabaseRegistry = None
    AsyncDatabaseRegistry = None


# Test configuration
TEST_DB_NAME = "erp_test"
TEST_ADDONS_PATH = "tests/test_addons"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_config():
    """Setup test configuration"""
    # Save original config
    original_config = config.config_file
    
    # Create temporary config
    test_config_content = """
[options]
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp
db_name = erp_test
addons_path = addons
http_port = 8069
http_interface = 127.0.0.1
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
        f.write(test_config_content)
        test_config_file = f.name
    
    # Apply test config
    config.config_file = test_config_file
    config._load_config()
    
    yield config
    
    # Cleanup
    os.unlink(test_config_file)
    config.config_file = original_config
    config._load_config()


@pytest.fixture
def clean_registry():
    """Clean model registry before each test"""
    # Save current state
    original_models = ModelRegistry._models.copy()
    original_async_models = AsyncModelRegistry._models.copy() if ASYNC_AVAILABLE else {}

    # Clear registries
    ModelRegistry._models.clear()
    if ASYNC_AVAILABLE:
        AsyncModelRegistry._models.clear()

    yield

    # Restore original state
    ModelRegistry._models = original_models
    if ASYNC_AVAILABLE:
        AsyncModelRegistry._models = original_async_models


@pytest.fixture
def addon_loader(test_config, clean_registry):
    """Create addon loader for testing"""
    loader = AddonLoader()
    yield loader


@pytest.fixture
async def async_addon_loader(test_config, clean_registry):
    """Create async addon loader for testing"""
    if not ASYNC_AVAILABLE:
        pytest.skip("Async functionality not available")
    loader = AsyncAddonLoader()
    yield loader


@pytest.fixture
def temp_addon_dir():
    """Create temporary addon directory"""
    temp_dir = tempfile.mkdtemp(prefix="test_addon_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def sample_addon_manifest():
    """Sample addon manifest data"""
    return {
        'name': 'Test Addon',
        'version': '1.0.0',
        'description': 'Test addon for unit tests',
        'author': 'Test Author',
        'depends': ['base'],
        'data': [],
        'installable': True,
        'auto_install': False,
        'category': 'Test',
    }


@pytest.fixture
def create_test_addon(temp_addon_dir, sample_addon_manifest):
    """Create a test addon in temporary directory"""
    def _create_addon(addon_name, manifest_data=None):
        if manifest_data is None:
            manifest_data = sample_addon_manifest.copy()
        
        addon_path = os.path.join(temp_addon_dir, addon_name)
        os.makedirs(addon_path, exist_ok=True)
        
        # Create __manifest__.py
        manifest_file = os.path.join(addon_path, '__manifest__.py')
        with open(manifest_file, 'w') as f:
            f.write(repr(manifest_data))
        
        # Create __init__.py
        init_file = os.path.join(addon_path, '__init__.py')
        with open(init_file, 'w') as f:
            f.write("# Test addon\n")
        
        # Create models directory
        models_dir = os.path.join(addon_path, 'models')
        os.makedirs(models_dir, exist_ok=True)
        
        models_init = os.path.join(models_dir, '__init__.py')
        with open(models_init, 'w') as f:
            f.write("# Models\n")
        
        return addon_path
    
    return _create_addon


@pytest.fixture
async def test_database():
    """Setup test database"""
    if not ASYNC_AVAILABLE:
        pytest.skip("Async database functionality not available")

    try:
        # Check if database exists
        if not await AsyncDatabaseRegistry.database_exists(TEST_DB_NAME):
            await AsyncDatabaseRegistry.create_database(TEST_DB_NAME)

        # Set as current database
        AsyncDatabaseRegistry.set_current_database(TEST_DB_NAME)

        yield TEST_DB_NAME

    except Exception as e:
        pytest.skip(f"Database not available: {e}")
    finally:
        # Cleanup
        try:
            await AsyncDatabaseRegistry.close_all()
        except:
            pass


@pytest.fixture
def mock_database():
    """Mock database for tests that don't need real database"""
    class MockDatabase:
        def __init__(self):
            self.data = {}
        
        async def fetchval(self, query, *args):
            return "mock_result"
        
        async def fetch(self, query, *args):
            return []
        
        async def execute(self, query, *args):
            return "OK"
        
        def transaction(self):
            return self
        
        async def __aenter__(self):
            return self
        
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            pass
    
    return MockDatabase()


@pytest.fixture
def field_test_data():
    """Test data for field validation tests"""
    return {
        'char_valid': ['test', 'hello world', '123'],
        'char_invalid': [None],  # when required=True
        'integer_valid': [1, 42, -5, '123'],
        'integer_invalid': ['not_a_number', 3.14],
        'float_valid': [1.0, 3.14, '2.5', 42],
        'float_invalid': ['not_a_number'],
        'boolean_valid': [True, False, 'true', 'false', '1', '0', 1, 0],
        'boolean_invalid': ['maybe', 'invalid'],
        'date_valid': ['2023-01-15', '15/01/2023'],
        'date_invalid': ['invalid_date', '2023-13-45'],
        'selection_valid': ['draft', 'done'],
        'selection_invalid': ['invalid_option'],
    }


@pytest.fixture
def performance_timer():
    """Timer fixture for performance tests"""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


# Pytest hooks for custom behavior

def pytest_configure(config):
    """Configure pytest with custom settings"""
    # Add custom markers
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "async_test: Asynchronous tests")
    config.addinivalue_line("markers", "performance: Performance tests")
    config.addinivalue_line("markers", "slow: Slow running tests")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically"""
    for item in items:
        # Add async marker to async tests
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.async_test)
        
        # Add unit marker to tests in unit test directories
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        
        # Add integration marker to integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)


def pytest_runtest_setup(item):
    """Setup for each test"""
    # Skip database tests if no database available
    if item.get_closest_marker("database"):
        try:
            # Quick database connectivity check
            pass  # Add actual check if needed
        except Exception:
            pytest.skip("Database not available")


# HTML report customization would require pytest-html plugin
# def pytest_html_report_title(report):
#     """Customize HTML report title"""
#     report.title = "ERP System Test Report"
